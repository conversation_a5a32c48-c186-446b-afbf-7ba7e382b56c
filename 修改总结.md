# SAR视频动目标检测系统修改总结

## 修改概述

根据您的要求，我已经成功修改了动目标检测代码，实现了以下两个主要功能：

1. **只绘制目标框而不绘制轨迹**
2. **加入三帧间差分法联合提取动目标**

## 主要修改文件

### 1. detection.py
- **新增功能**：
  - `three_frame_difference()`: 实现三帧间差分法
  - `combine_detection_results()`: 结合阴影检测和帧差分结果
  - `regions_overlap()`: 检查区域重叠
  - `extract_regions_from_mask()`: 从二值掩码提取区域
  
- **修改功能**：
  - `MotionDetector.__init__()`: 添加帧差分相关参数
  - `detect_from_folder()`: 集成三帧差分法
  - `visualize_trajectories()` → `visualize_detections()`: 只绘制目标框

### 2. main.py
- **新增参数**：
  - `use_frame_difference`: 是否启用三帧间差分法
  - `diff_threshold`: 帧差分阈值
  - `segmented_folder`: 分割图像文件夹路径

### 3. test_detection.py
- 更新测试脚本以使用新参数

### 4. 新增文件
- `README_动目标检测_更新.md`: 详细说明文档
- `demo_new_detection.py`: 演示脚本
- `修改总结.md`: 本文件

## 核心算法改进

### 三帧间差分法
```python
def three_frame_difference(self, frame1, frame2, frame3):
    # 计算相邻帧间差分
    diff1 = cv2.absdiff(frame2, frame1)  # |frame2 - frame1|
    diff2 = cv2.absdiff(frame3, frame2)  # |frame3 - frame2|
    
    # 阈值处理
    _, binary1 = cv2.threshold(diff1, self.diff_threshold, 255, cv2.THRESH_BINARY)
    _, binary2 = cv2.threshold(diff2, self.diff_threshold, 255, cv2.THRESH_BINARY)
    
    # 取交集
    motion_mask = cv2.bitwise_and(binary1, binary2)
    
    # 形态学去噪
    kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
    motion_mask = cv2.morphologyEx(motion_mask, cv2.MORPH_OPEN, kernel)
    motion_mask = cv2.morphologyEx(motion_mask, cv2.MORPH_CLOSE, kernel)
    
    return motion_mask
```

### 联合检测策略
1. 从后处理图像提取阴影区域
2. 使用分割图像进行三帧差分
3. 计算阴影区域与帧差分结果的重叠度
4. 合并检测结果，避免重复

### 可视化改进
- 移除轨迹线绘制
- 只显示当前帧的目标边界框
- 添加目标ID和面积信息
- 使用更粗的边界框（3像素）和更大的质心点（6像素半径）

## 使用方法

### 命令行运行
```bash
# 使用默认参数（启用三帧差分）
python main.py --video SAR_Video_First.mp4

# 自定义参数
python main.py --video SAR_Video_First.mp4 \
    --use_frame_difference True \
    --diff_threshold 25 \
    --min_displacement 30.0
```

### 直接调用
```python
from detection import detect_motion_targets

motion_targets = detect_motion_targets(
    post_processed_folder="post_processed",
    original_frames_folder="registered", 
    segmented_folder="segmented",        # 新增
    output_folder="detected",
    use_frame_difference=True,           # 新增
    diff_threshold=30                    # 新增
)
```

### 演示脚本
```bash
python demo_new_detection.py
```

## 参数说明

### 新增参数
- `use_frame_difference` (bool): 是否启用三帧间差分法，默认True
- `diff_threshold` (int): 帧差分阈值，默认30
- `segmented_folder` (str): 分割后图像文件夹，默认"segmented"

### 调优建议
- **diff_threshold**:
  - 20-25: 对微小运动敏感，适合慢速目标
  - 30-35: 减少噪声，适合快速目标
- **min_displacement**: 根据目标运动速度调整
- **min_trajectory_length**: 根据视频帧率调整

## 输出结果

### 可视化图像
- 保存在指定的输出文件夹中
- 只显示目标边界框和质心
- 不显示运动轨迹线
- 包含目标ID和面积信息

### 检测结果文件
- `detection_results.txt`: 详细统计信息
- 包含每个目标的轨迹数据

## 性能优化

1. **内存管理**: 三帧缓冲区自动管理
2. **计算效率**: 只在有足够帧时进行差分
3. **结果融合**: 智能合并，避免重复
4. **形态学优化**: 使用椭圆核去噪

## 兼容性

- 保持与原有代码的兼容性
- 可以通过参数控制是否启用新功能
- 支持原有的所有检测参数

## 测试建议

1. 先使用默认参数测试
2. 根据检测结果调整diff_threshold
3. 观察可视化结果的目标框准确性
4. 比较启用/禁用帧差分的效果差异

## 注意事项

1. 确保segmented文件夹存在且包含分割图像
2. 三帧差分需要至少3帧才能开始工作
3. 帧差分阈值需要根据场景调整
4. 建议先用演示脚本测试功能
