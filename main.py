import cv2
import argparse
from tqdm import tqdm
import torch
from registration import register_frames
from segmentation import segment_image
from utils import create_output_dirs, save_frame
from cattepm_filter import cattepm_denoising
from post_processing import post_process
from detection import detect_motion_targets

def process_video(video_path, output_video_path=None,
                  seg_method='otsu',
                  # CattePM降噪参数
                  sigma=2.0, lambda_val=0.125, iterations=30,
                  # 动目标检测参数
                  min_displacement=15.0, min_trajectory_length=3, max_distance=40.0,
                  min_area_similarity=0.2, min_area_threshold=30):
    """
    处理SAR视频，检测动目标

    参数:
        video_path: 输入视频路径
        output_video_path: 输出视频路径，如果为None则使用默认路径
        seg_method: 分割方法，可选 'otsu' 或 'adaptive'
        sigma: CattePM方差σ（滤波尺度）
        lambda_val: CattePM扩散程度常数λ
        iterations: CattePM迭代次数n
    """
    # 创建输出目录
    create_output_dirs()

    # 打开视频文件
    cap = cv2.VideoCapture(video_path)
    if not cap.isOpened():
        print(f"无法打开视频: {video_path}")
        return

    # 获取视频属性
    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    fps = cap.get(cv2.CAP_PROP_FPS)
    frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))

    # 设置输出视频
    if output_video_path is None:
        output_video_path = 'result_video.mp4'

    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    out = cv2.VideoWriter(output_video_path, fourcc, fps, (width, height))

    # 读取第一帧
    ret, prev_frame = cap.read()
    if not ret:
        print("无法读取视频帧")
        return

    # 存储所有处理后的帧
    all_frames = [prev_frame]
    registered_frames = [prev_frame]  # 第一帧不需要配准

    # 对第一帧进行CattePM降噪处理
    denoised_frame = cattepm_denoising(prev_frame, sigma=sigma, lambda_val=lambda_val, iterations=iterations)
    denoised_frames = [denoised_frame]

    # 对降噪后的图像进行Otsu阈值分割
    segmented_frame = segment_image(denoised_frame, method=seg_method)
    segmented_frames = [segmented_frame]

    # 对阈值分割后的图像进行后处理
    post_processed_frame = post_process(segmented_frame, kernel_size_open=(7, 7), kernel_size_close=(7, 7))
    post_processed_frames = [post_processed_frame]

    # 保存第一帧结果
    save_frame(prev_frame, 0, 'registered', 'reg_')
    save_frame(denoised_frame, 0, 'cattePM', 'cattepm_')  # 保存到cattePM文件夹
    save_frame(segmented_frame, 0, 'segmented', 'seg_')
    save_frame(post_processed_frame, 0, 'post_processed', 'post_')



    # 处理剩余帧
    frame_idx = 1
    pbar = tqdm(total=frame_count, desc="处理视频帧")

    while True:
        ret, current_frame = cap.read()
        if not ret:
            break

        # 图像配准
        registered_frame, _ = register_frames(prev_frame, current_frame)

        # 保存配准结果
        save_frame(registered_frame, frame_idx, 'registered', 'reg_')

        # 降噪 - 使用CattePM滤波器
        denoised_frame = cattepm_denoising(registered_frame, sigma=sigma, lambda_val=lambda_val, iterations=iterations)
        save_frame(denoised_frame, frame_idx, 'cattePM', 'cattepm_')  # 保存到cattePM文件夹

        # 对降噪后的图像进行Otsu阈值分割
        segmented_frame = segment_image(denoised_frame, method=seg_method)
        save_frame(segmented_frame, frame_idx, 'segmented', 'seg_')


        # 后处理
        post_processed_frame = post_process(segmented_frame, kernel_size_open = (7, 7), kernel_size_close = (7, 7))
        save_frame(post_processed_frame, frame_idx, 'post_processed', 'post_')


        # 存储处理后的帧
        all_frames.append(current_frame)
        registered_frames.append(registered_frame)
        denoised_frames.append(denoised_frame)
        segmented_frames.append(segmented_frame)
        post_processed_frames.append(post_processed_frame)


        # 更新前一帧
        prev_frame = registered_frame.copy()

        frame_idx += 1
        pbar.update(1)

    pbar.close()
    cap.release()

    # 执行动目标检测
    print("\n开始动目标检测...")
    motion_trajectories = detect_motion_targets(
        post_processed_folder="post_processed",
        original_frames_folder="registered",
        output_folder="detected",
        min_displacement=min_displacement,
        min_trajectory_length=min_trajectory_length,
        max_distance=max_distance,
        min_area_similarity=min_area_similarity,
        min_area_threshold=min_area_threshold
    )

    print(f"\n动目标检测完成！检测到 {len(motion_trajectories)} 个运动目标")
    return motion_trajectories



if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='SAR视频动目标检测')
    parser.add_argument('--video', type=str, default='1.mp4', help='输入SAR视频路径')
    parser.add_argument('--output', type=str, default='result_video.mp4', help='输出视频路径')

    # CattePM降噪参数
    parser.add_argument('--sigma', type=float, default=2.0,
                        help='CattePM方差σ（滤波尺度），默认2.0')
    parser.add_argument('--lambda_val', type=float, default=0.125,
                        help='CattePM扩散程度常数λ，默认0.125')
    parser.add_argument('--iterations', type=int, default=30,
                        help='CattePM迭代次数n，默认30')

    # 分割参数
    parser.add_argument('--seg_method', type=str, default='otsu',
                        choices=['otsu', 'global', 'adaptive'],
                        help="分割方法: 'otsu' (或 'global') 使用Otsu全局阈值, 'adaptive' 使用自适应阈值。")

    # 动目标检测参数
    parser.add_argument('--min_displacement', type=float, default=50.0,
                        help='运动目标最小位移阈值（像素）')
    parser.add_argument('--min_trajectory_length', type=int, default=8,
                        help='运动目标最小轨迹长度（帧数）')
    parser.add_argument('--max_distance', type=float, default=30.0,
                        help='阴影区域匹配最大距离（像素）')
    parser.add_argument('--min_area_similarity', type=float, default=0.2,
                        help='阴影区域匹配最小面积相似度（0-1）')
    parser.add_argument('--min_area_threshold', type=int, default=50,
                        help='阴影区域最小面积阈值（像素²）')


    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f" Using device: {device}")

    args = parser.parse_args()

    # 显示CattePM参数
    print(f"CattePM降噪参数:")
    print(f"  方差σ (滤波尺度): {args.sigma}")
    print(f"  扩散程度常数λ: {args.lambda_val}")
    print(f"  迭代次数n: {args.iterations}")

    process_video(args.video, args.output,
                  seg_method=args.seg_method,
                  sigma=args.sigma,
                  lambda_val=args.lambda_val,
                  iterations=args.iterations,
                  min_displacement=args.min_displacement,
                  min_trajectory_length=args.min_trajectory_length,
                  max_distance=args.max_distance,
                  min_area_similarity=args.min_area_similarity,
                  min_area_threshold=args.min_area_threshold)
